import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios';

interface Options {
  appKey: string;
  secretKey?: string;
  host: string;
  raw?: boolean;
}

interface CacheEntry {
  accessToken: string;
  expireTime: number;
  createdAt: number;
}

// 简单的内存缓存
class MemoryCache {
  private cache = new Map<string, CacheEntry>();

  get(key: string): string | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // 检查是否过期
    const now = Date.now();
    if (now - entry.createdAt > entry.expireTime * 1000) {
      this.cache.delete(key);
      return null;
    }

    return entry.accessToken;
  }

  set(key: string, accessToken: string, expireTime: number): void {
    this.cache.set(key, {
      accessToken,
      expireTime,
      createdAt: Date.now(),
    });
  }

  delete(key: string): void {
    this.cache.delete(key);
  }
}

export class OpenApiClient {
  private cache = new MemoryCache();

  async request<T>({
    url,
    method,
    body,
    headers,
    options,
  }: {
    url: string;
    method: string;
    body?: Record<string, any>;
    headers?: Record<string, string | number>;
    options: Options;
  }): Promise<T> {
    const { accessToken, delHandler } =
      await this.getAccessTokenWithCache(options);

    const _url = `${options.host}${url}`;

    try {
      const response = await axios({
        url: _url,
        method,
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          ...headers,
        },
        data: body,
      } as AxiosRequestConfig);

      if (response.status === 200) {
        return response.data;
      }

      // 进入兜底 catch 处理, 删除 accessToken 上游重新调用
      throw response;
    } catch (error) {
      await delHandler();
      throw error;
    }
  }

  /**
   * 添加缓存
   */
  private async getAccessTokenWithCache(options: Options): Promise<{
    accessToken: string;
    delHandler: () => Promise<void>;
  }> {
    const key = `OPENAPI_ACCESSTOKEN::${options.host}_${options.appKey}`;

    const accessToken = this.cache.get(key);

    const delHandler = async () => {
      this.cache.delete(key);
      console.info(`[open-api-client] delete accessToken succeeded`);
    };

    if (accessToken) {
      console.info(
        `[open-api-client] get accessToken from cache accessToken=${accessToken}`,
      );
      return {
        accessToken,
        delHandler,
      };
    }

    const result = await this.getAccessToken(options);

    this.cache.set(key, result.accessToken, result.expireTime);

    console.info(
      `[open-api-client] get accessToken from remote api accessToken=${result.accessToken}`,
    );

    return {
      accessToken: result.accessToken || '',
      delHandler,
    };
  }

  private async getAccessToken(options: Options): Promise<{
    expireTime: number;
    accessToken: string;
  }> {
    let url = `${options.host}/token/get?appKey=${options.appKey}`;
    if (options.secretKey) {
      url = `${url}&secretKey=${options.secretKey}`;
    }

    const response = await axios.get(url);
    const res: {
      code: number;
      result: { expireTime: number; accessToken: string };
    } = response.data;

    if (res.code !== 0) {
      throw new Error(`Failed to get access token: ${JSON.stringify(res)}`);
    }

    return res.result;
  }
}

// 创建默认实例
export const openApiClient = new OpenApiClient();

// 从环境变量获取配置
export function getDocsAPIConfig(): Options {
  return {
    host: process.env.DOCS_API_HOST || 'https://is-gateway.corp.kuaishou.com',
    appKey:
      process.env.DOCS_API_APP_KEY || '618b78c0-160e-4b08-92d6-96edb3b2ebfc',
    secretKey:
      process.env.DOCS_API_SECRET_KEY || '48c92cac-ec3b-4331-bbcd-1526987aae9b',
  };
}

// 文档获取函数
export async function fetchDocumentContent(url: string): Promise<string> {
  console.log('获取文档:', url);

  try {
    // 从URL中提取docId
    // URL格式: https://docs.corp.kuaishou.com/k/home/<USER>/fcADhTLvXIk4XVLFGoYnEgXun
    // docId是最后一个路径段
    const urlParts = url.split('/');
    const docId = urlParts[urlParts.length - 1];

    if (!docId || docId.length === 0) {
      throw new Error('无法从URL中提取docId');
    }

    console.log('提取到的docId:', docId);

    // 使用OpenApiClient获取文档内容
    const response = await openApiClient.request<any>({
      url: `/word/e/api/get_md_content?docId=${docId}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      options: getDocsAPIConfig(),
    });

    // 解析响应内容
    let content = '';
    if (typeof response === 'string') {
      content = response;
    } else if (response && response.data && response.data.content) {
      content = response.data.content;
    } else if (response && response.content) {
      content = response.content;
    } else {
      content = JSON.stringify(response);
    }

    console.log('Document fetched successfully, length:', content.length);

    return `文档内容已获取成功，内容长度: ${content.length} 字符\n\n文档内容:\n${content}`;
  } catch (error) {
    console.error('Error fetching document:', error);
    throw new Error(`获取文档失败: ${error.message}`);
  }
}
