{"name": "prd-planner-graph", "version": "1.0.0", "description": "LangGraph implementation for PRD planning and task decomposition", "type": "module", "main": "graph.js", "scripts": {"dev": "langgraph dev", "test": "tsx test-graph.ts", "build": "tsc", "viz": "langgraph viz --graph prd_planner"}, "dependencies": {"@langchain/langgraph": "^0.2.0", "@langchain/openai": "^0.3.0", "@langchain/core": "^0.3.0", "zod": "^3.22.0"}, "devDependencies": {"@langchain/langgraph-cli": "^0.1.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}}