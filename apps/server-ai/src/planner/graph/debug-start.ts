#!/usr/bin/env node

import { createGraph } from './graph.js';
import { HumanMessage } from '@langchain/core/messages';

async function startDebug() {
  console.log('🚀 PRD Planner Graph 调试启动');
  console.log('==============================\n');

  // 步骤1：先运行诊断
  console.log('📋 步骤1: 运行连接诊断');
  console.log('建议先运行: npx tsx diagnose.ts');
  console.log('这将检查网络连接和API配置\n');

  // 步骤2：创建图
  console.log('📋 步骤2: 创建图实例');
  try {
    const graph = createGraph();
    console.log('✅ 图创建成功\n');

    // 步骤3：准备测试数据
    console.log('📋 步骤3: 准备测试数据');
    const testCases = [
      {
        name: '简单PRD请求',
        input: {
          originalPRD: "我需要开发一个用户管理系统",
          structuredPRD: "",
          taskJSON: "",
          humanFeedback: "",
          isComplete: false,
          fullResponse: "",
          isPRDRequest: false,
          coordinatorResponse: "",
          documentContent: "",
          messages: [new HumanMessage("我需要开发一个用户管理系统")]
        }
      },
      {
        name: '非PRD请求',
        input: {
          originalPRD: "今天天气怎么样？",
          structuredPRD: "",
          taskJSON: "",
          humanFeedback: "",
          isComplete: false,
          fullResponse: "",
          isPRDRequest: false,
          coordinatorResponse: "",
          documentContent: "",
          messages: [new HumanMessage("今天天气怎么样？")]
        }
      }
    ];

    console.log(`✅ 准备了 ${testCases.length} 个测试用例\n`);

    // 步骤4：选择测试用例
    console.log('📋 步骤4: 开始测试');
    
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`\n🧪 测试用例 ${i + 1}: ${testCase.name}`);
      console.log('─'.repeat(40));
      
      try {
        const config = { 
          configurable: { 
            thread_id: `test-${i}-${Date.now()}` 
          } 
        };

        console.log('⏳ 执行中...');
        const startTime = Date.now();
        
        const result = await graph.invoke(testCase.input, config);
        
        const duration = Date.now() - startTime;
        console.log(`✅ 执行成功 (${duration}ms)`);
        
        // 显示结果摘要
        console.log('📊 结果摘要:');
        console.log(`   - PRD请求: ${result.isPRDRequest}`);
        console.log(`   - 完成状态: ${result.isComplete}`);
        console.log(`   - 消息数量: ${result.messages?.length || 0}`);
        
        if (result.coordinatorResponse) {
          console.log(`   - 响应: ${result.coordinatorResponse.substring(0, 100)}...`);
        }
        
        if (result.structuredPRD) {
          console.log(`   - 结构化PRD: ${result.structuredPRD.length} 字符`);
        }
        
      } catch (error) {
        console.log(`❌ 执行失败: ${error.message}`);
        
        // 提供具体的错误建议
        if (error.message.includes('AbortError') || error.message.includes('Request was aborted')) {
          console.log('💡 建议: 网络请求被中断，请检查:');
          console.log('   1. 网络连接是否稳定');
          console.log('   2. 是否需要VPN');
          console.log('   3. API服务是否可用');
        }
        
        if (error.message.includes('Chat is not supported')) {
          console.log('💡 建议: 状态定义问题，请检查 messages 字段配置');
        }
      }
    }

    console.log('\n🏁 测试完成');
    console.log('\n📚 更多调试选项:');
    console.log('1. 运行诊断: npx tsx diagnose.ts');
    console.log('2. 快速测试: npx tsx quick-test.ts');
    console.log('3. 启动Studio: langgraph dev');
    console.log('4. 查看图结构: langgraph graph --graph prd_planner');

  } catch (error) {
    console.log('❌ 图创建失败:', error.message);
    console.log('\n💡 可能的解决方案:');
    console.log('1. 检查依赖是否正确安装');
    console.log('2. 检查导入路径是否正确');
    console.log('3. 检查TypeScript编译是否成功');
  }
}

// 运行调试
startDebug().catch(console.error);
