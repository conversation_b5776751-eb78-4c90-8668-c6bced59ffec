import { ChatOpenAI } from '@langchain/openai';
import axios from 'axios';

async function diagnoseConnection() {
  console.log('🔍 开始诊断连接问题');
  console.log('===================');

  // 1. 检查环境变量
  console.log('1️⃣ 检查环境变量...');
  const apiKey = process.env.OPENAI_API_KEY || 'TXI1OjwcUJORWR';
  const baseURL = 'https://ai-gateway.corp.kuaishou.com/v2';
  
  console.log('✅ API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : '未设置');
  console.log('✅ Base URL:', baseURL);

  // 2. 测试网络连接
  console.log('\n2️⃣ 测试网络连接...');
  try {
    const response = await axios.get('https://ai-gateway.corp.kuaishou.com', {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0'
      }
    });
    console.log('✅ 网络连接正常，状态码:', response.status);
  } catch (error) {
    console.log('❌ 网络连接失败:', error.message);
    if (error.code === 'ENOTFOUND') {
      console.log('💡 建议: 检查网络连接或VPN设置');
    }
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 建议: 服务器可能不可用');
    }
    if (error.code === 'ETIMEDOUT') {
      console.log('💡 建议: 网络超时，尝试增加超时时间');
    }
  }

  // 3. 测试简单的 LLM 调用
  console.log('\n3️⃣ 测试 LLM 连接...');
  try {
    const llm = new ChatOpenAI({
      model: 'gpt-4o',
      apiKey: apiKey,
      timeout: 30000, // 30秒超时
      configuration: {
        baseURL: baseURL,
        defaultHeaders: {
          'x-dmo-provider': 'openai',
        },
      },
    });

    console.log('⏳ 发送测试请求...');
    const response = await llm.invoke('Hello, this is a test message.');
    console.log('✅ LLM 调用成功!');
    console.log('📝 响应:', response.content.toString().substring(0, 100) + '...');
    
  } catch (error) {
    console.log('❌ LLM 调用失败:', error.message);
    
    if (error.message.includes('AbortError')) {
      console.log('💡 这是请求中断错误，可能的原因:');
      console.log('   - 网络不稳定');
      console.log('   - 请求超时');
      console.log('   - 服务器负载过高');
    }
    
    if (error.message.includes('401')) {
      console.log('💡 认证失败，请检查 API 密钥');
    }
    
    if (error.message.includes('403')) {
      console.log('💡 权限不足，请检查 API 密钥权限');
    }
    
    if (error.message.includes('429')) {
      console.log('💡 请求频率过高，请稍后重试');
    }
    
    if (error.message.includes('500')) {
      console.log('💡 服务器内部错误');
    }
  }

  // 4. 测试文档 API
  console.log('\n4️⃣ 测试文档 API 连接...');
  try {
    const docsHost = process.env.DOCS_API_HOST || 'https://is-gateway.corp.kuaishou.com';
    const appKey = process.env.DOCS_API_APP_KEY || '618b78c0-160e-4b08-92d6-96edb3b2ebfc';
    
    console.log('📡 文档 API 主机:', docsHost);
    console.log('🔑 App Key:', appKey ? `${appKey.substring(0, 8)}...` : '未设置');
    
    // 测试获取 token
    const tokenUrl = `${docsHost}/token/get?appKey=${appKey}`;
    const tokenResponse = await axios.get(tokenUrl, { timeout: 10000 });
    
    if (tokenResponse.data.code === 0) {
      console.log('✅ 文档 API 认证成功');
    } else {
      console.log('❌ 文档 API 认证失败:', tokenResponse.data);
    }
    
  } catch (error) {
    console.log('❌ 文档 API 测试失败:', error.message);
  }

  console.log('\n🏁 诊断完成');
  console.log('\n💡 解决建议:');
  console.log('1. 确保网络连接稳定');
  console.log('2. 检查是否需要VPN连接');
  console.log('3. 验证 API 密钥是否正确');
  console.log('4. 尝试增加请求超时时间');
  console.log('5. 检查防火墙设置');
}

// 运行诊断
diagnoseConnection().catch(console.error);
