import { createGraph } from './graph.js';

async function testGraph() {
  const graph = createGraph();
  
  // 测试用例1：简单的PRD请求
  const testInput1 = {
    originalPRD: "我需要开发一个用户管理系统，包含用户注册、登录、个人信息管理等功能。",
    structuredPRD: "",
    taskJSON: "",
    humanFeedback: "",
    isComplete: false,
    fullResponse: "",
    isPRDRequest: false,
    coordinatorResponse: "",
    documentContent: "",
    messages: []
  };

  console.log('=== 测试用例1：简单PRD请求 ===');
  console.log('输入:', testInput1.originalPRD);
  
  try {
    const config = { configurable: { thread_id: "test-1" } };
    const result = await graph.invoke(testInput1, config);
    console.log('结果:', result);
  } catch (error) {
    console.error('测试失败:', error);
  }

  // 测试用例2：包含文档链接的请求
  const testInput2 = {
    originalPRD: "请帮我分析这个文档中的需求：https://docs.corp.kuaishou.com/k/home/<USER>/fcADhTLvXIk4XVLFGoYnEgXun",
    structuredPRD: "",
    taskJSON: "",
    humanFeedback: "",
    isComplete: false,
    fullResponse: "",
    isPRDRequest: false,
    coordinatorResponse: "",
    documentContent: "",
    messages: []
  };

  console.log('\n=== 测试用例2：包含文档链接的请求 ===');
  console.log('输入:', testInput2.originalPRD);
  
  try {
    const config = { configurable: { thread_id: "test-2" } };
    const result = await graph.invoke(testInput2, config);
    console.log('结果:', result);
  } catch (error) {
    console.error('测试失败:', error);
  }

  // 测试用例3：非PRD请求
  const testInput3 = {
    originalPRD: "今天天气怎么样？",
    structuredPRD: "",
    taskJSON: "",
    humanFeedback: "",
    isComplete: false,
    fullResponse: "",
    isPRDRequest: false,
    coordinatorResponse: "",
    documentContent: "",
    messages: []
  };

  console.log('\n=== 测试用例3：非PRD请求 ===');
  console.log('输入:', testInput3.originalPRD);
  
  try {
    const config = { configurable: { thread_id: "test-3" } };
    const result = await graph.invoke(testInput3, config);
    console.log('结果:', result);
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testGraph().catch(console.error);
}

export { testGraph };
