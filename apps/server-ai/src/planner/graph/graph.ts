import {
  StateGraph,
  Annotation,
  START,
  END,
  interrupt,
  Command,
  MemorySaver,
} from '@langchain/langgraph';
import { AIMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';
import { fetchDocumentContent } from './openapi.js';

// 定义状态结构
const StateAnnotation = Annotation.Root({
  originalPRD: Annotation<string>,
  structuredPRD: Annotation<string>,
  taskJSON: Annotation<string>,
  humanFeedback: Annotation<string>,
  isComplete: Annotation<boolean>,
  fullResponse: Annotation<string>,
  isPRDRequest: Annotation<boolean>,
  coordinatorResponse: Annotation<string>,
  documentContent: Annotation<string>,
  messages: Annotation<any[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
});

// 创建 LLM 实例
function createLLM() {
  return new ChatOpenAI({
    model: 'gpt-4o',
    apiKey: 'TXI1OjwcUJORWR',
    configuration: {
      baseURL: 'https://ai-gateway.corp.kuaishou.com/v2',
      defaultHeaders: {
        'x-dmo-provider': 'openai',
      },
    },
  });
}

// 创建工具
function createTools() {
  // 定义 handoff_to_planner 工具
  const handoffToPlannerTool = tool(
    ({ reason }: { reason: string }) => {
      console.log('Handoff to planner tool called with reason:', reason);
      return;
    },
    {
      name: 'handoff_to_planner',
      description:
        'Hand off the conversation to the planner when a PRD request is identified',
      schema: z.object({
        reason: z
          .string()
          .describe('The reason for handing off to the planner'),
      }),
    },
  );

  // 定义 fetch_document 工具
  const fetchDocumentTool = tool(
    async ({ url }: { url: string }) => {
      console.log('Fetch document tool called with URL:', url);
      return await fetchDocumentContent(url);
    },
    {
      name: 'fetch_document',
      description:
        'Fetch content from a document URL when user provides a link',
      schema: z.object({
        url: z.string().describe('The URL of the document to fetch'),
      }),
    },
  );

  return [handoffToPlannerTool, fetchDocumentTool];
}

// 工具节点 - 处理工具执行
async function toolsNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('Tools节点开始执行工具...');

  // 获取最后一条消息中的工具调用
  const lastMessage = state.messages[state.messages.length - 1];
  if (!lastMessage?.tool_calls || lastMessage.tool_calls.length === 0) {
    console.log('没有找到工具调用，返回coordinator');
    return new Command({
      goto: 'coordinator',
    });
  }

  const toolCall = lastMessage.tool_calls[0];
  console.log('执行工具:', toolCall.name, toolCall.args);

  try {
    // 根据工具名称执行相应的工具
    if (toolCall.name === 'fetch_document') {
      const { url } = toolCall.args as { url: string };
      const documentContent = await fetchDocumentContent(url);

      // 返回到coordinator进行PRD判断
      return new Command({
        goto: 'coordinator',
        update: {
          documentContent: documentContent,
        },
      });
    } else if (toolCall.name === 'handoff_to_planner') {
      console.log('handoff_to_planner工具调用，路由到PM节点');
      return new Command({
        goto: 'pm',
        update: {
          isPRDRequest: true,
        },
      });
    }

    // 未知工具，返回coordinator
    console.log('未知工具:', toolCall.name);
    return new Command({
      goto: 'coordinator',
    });
  } catch (error) {
    console.error('工具执行失败:', error);

    // 如果是fetch_document失败，设置错误信息并返回coordinator
    if (toolCall.name === 'fetch_document') {
      return new Command({
        goto: 'coordinator',
        update: {
          documentContent: `获取文档失败: ${error.message}`,
        },
      });
    }

    // 其他错误，直接返回coordinator
    return new Command({
      goto: 'coordinator',
    });
  }
}

// Coordinator节点 - 判断是否为PRD需求
async function coordinatorNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('Coordinator节点开始判断用户输入...', state.originalPRD);

  const llm = createLLM();
  const tools = createTools();
  const coordinatorLLM = llm.bindTools(tools);

  // 如果已经有文档内容，说明是从工具节点返回的，直接进行PRD判断
  if (state.documentContent) {
    console.log('检测到已获取的文档内容，进行PRD判断');
    const combinedInput = `${state.originalPRD}\n\n获取的文档内容：\n${state.documentContent}`;

    const prdJudgmentPrompt = `
作为智能助手协调员，请判断以下内容是否为产品需求文档(PRD)相关的请求：

用户输入和文档内容：
${combinedInput}

判断标准：
1. 是否包含产品功能描述
2. 是否包含需求说明
3. 是否要求进行产品规划或任务分解
4. 是否涉及产品开发相关内容

如果是PRD相关请求，请调用 handoff_to_planner 工具并提供判断理由。
如果不是PRD请求，请直接回复礼貌的说明信息。
`;

    const response = await coordinatorLLM.invoke(prdJudgmentPrompt);

    if (response.tool_calls && response.tool_calls.length > 0) {
      const toolCall = response.tool_calls[0];
      if (toolCall.name === 'handoff_to_planner') {
        console.log('识别为PRD请求，路由到PM节点');
        return new Command({
          goto: 'pm',
          update: {
            isPRDRequest: true,
            originalPRD: combinedInput, // 更新为包含文档内容的完整输入
          },
        });
      }
    }

    // 不是PRD请求
    const userMessage =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    return new Command({
      goto: END,
      update: {
        isPRDRequest: false,
        coordinatorResponse: userMessage,
        messages: [
          new AIMessage({
            content: userMessage,
            name: 'coordinator',
          }),
        ],
      },
    });
  }

  // 初次判断：检查是否包含文档链接或是否为PRD请求
  const prompt = `
作为智能助手协调员，请判断以下用户输入是否为产品需求文档(PRD)相关的请求，或者是否包含文档链接需要获取：

用户输入：
${state.originalPRD}

判断标准：
1. 如果用户提供了文档链接（http/https URL），请调用 fetch_document 工具获取文档内容
2. 是否包含产品功能描述
3. 是否包含需求说明
4. 是否要求进行产品规划或任务分解
5. 是否涉及产品开发相关内容

处理逻辑：
- 如果包含文档链接，先调用 fetch_document 工具获取内容
- 如果是PRD相关请求，请调用 handoff_to_planner 工具并提供判断理由
- 如果不是PRD请求，请直接回复礼貌的说明信息

礼貌回复模板：
很抱歉，我是专门处理产品需求文档(PRD)分析和任务分解的助手。您的问题似乎不在我的专业范围内。

我的主要功能包括：
- 分析和结构化产品需求文档
- 将PRD转换为具体的开发任务
- 提供技术实现建议和项目规划

如果您有产品需求相关的问题，我很乐意为您提供帮助！
`;

  try {
    const response = await coordinatorLLM.invoke(prompt);
    console.log('Coordinator节点完成判断', response);

    // 检查是否有工具调用
    if (response.tool_calls && response.tool_calls.length > 0) {
      console.log('检测到工具调用，路由到工具节点');
      return new Command({
        goto: 'tools',
        update: {
          messages: [response], // 将包含工具调用的响应传递给工具节点
        },
      });
    }

    // 如果没有工具调用，说明不是PRD请求
    console.log('非PRD请求，返回礼貌响应并结束');
    const userMessage =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    return new Command({
      goto: END,
      update: {
        isPRDRequest: false,
        coordinatorResponse: userMessage,
        messages: [
          new AIMessage({
            content: userMessage,
            name: 'coordinator',
          }),
        ],
      },
    });
  } catch (error) {
    console.error('Coordinator节点处理失败:', error);
    throw error;
  }
}

// PM节点 - 处理PRD
async function pmNode(state: typeof StateAnnotation.State): Promise<Command> {
  console.log('PM节点开始处理原始PRD...');

  const llm = createLLM();

  const prompt = `
作为产品经理，请将以下原始PRD转换为结构化的XML格式PRD，并评估信息完整性：

原始PRD：
${state.originalPRD}

请按照以下XML格式输出：

  <evaluation>
    <completeness>COMPLETE/INCOMPLETE</completeness>
    <missing_info>如果不完整，请描述缺少的关键信息</missing_info>
  </evaluation>
  <content>
    <message>给用户的友好消息</message>
    <prd>
      <title>产品标题</title>
      <overview>产品概述</overview>
      <requirements>
        <requirement id="1">需求1</requirement>
        <requirement id="2">需求2</requirement>
      </requirements>
      <acceptance_criteria>验收标准</acceptance_criteria>
    </prd>
  </content>

评估标准：
- 产品标题是否明确
- 产品概述是否详细
- 需求是否具体可执行
- 验收标准是否明确
- 目标用户是否明确
- 技术要求是否清晰

消息内容指导：
- 如果信息完整：提供积极的确认消息，如"很好！我已经为您整理了完整的PRD，现在开始生成开发任务..."
- 如果信息不完整：友好地请求补充信息，如"我需要更多信息来完善PRD，请补充以下内容：[具体缺少的信息]"
`;

  try {
    const response = await llm.invoke(prompt);
    const responseContent =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    console.log('PM节点完成PRD结构化', responseContent);

    // 解析XML响应
    const completenessMatch = responseContent.match(
      /<completeness>(.*?)<\/completeness>/,
    );
    const isComplete = completenessMatch?.[1]?.trim() === 'COMPLETE';

    const messageMatch = responseContent.match(/<message>(.*?)<\/message>/s);
    const userMessage = messageMatch?.[1]?.trim() || '';

    const prdMatch = responseContent.match(/<prd>(.*?)<\/prd>/s);
    const structuredPRD = prdMatch?.[0] || '';

    // 根据完整性判断下一步
    if (isComplete) {
      console.log('PRD信息完整，返回完整PRD并进入Engineer节点');
      return new Command({
        goto: 'engineer',
        update: {
          structuredPRD: structuredPRD,
          isComplete: isComplete,
          fullResponse: responseContent,
          messages: [
            new AIMessage({
              content: userMessage,
              name: 'pm',
            }),
          ],
        },
      });
    } else {
      console.log('PRD信息不完整，返回补充信息请求');
      return new Command({
        goto: 'pmFeedback',
        update: {
          structuredPRD: structuredPRD,
          isComplete: isComplete,
          fullResponse: responseContent,
          messages: [
            new AIMessage({
              content: userMessage,
              name: 'pm',
            }),
          ],
        },
      });
    }
  } catch (error) {
    console.error('PM节点处理失败:', error);
    throw error;
  }
}

// 人工反馈节点 - PM阶段
function pmFeedbackNode(state: typeof StateAnnotation.State): Command {
  console.log('等待PM阶段人工反馈...');

  // 提取缺少的信息
  const missingInfoMatch = state.fullResponse?.match(/MISSING_INFO:\s*(.+)/);
  const missingInfo = missingInfoMatch ? missingInfoMatch[1] : '请完善PRD内容';

  const feedback = interrupt({
    question: '检测到PRD信息不完整，请补充缺少的内容',
    structuredPRD: state.structuredPRD,
    missingInfo: missingInfo,
    fullResponse: state.fullResponse,
    action: '请选择: approve(批准) 或 revise(修改)',
    instructions: '如果需要修改，请在feedback字段中提供具体的修改建议',
  });

  // 当恢复执行时，这里的逻辑会被执行
  console.log('收到PM反馈:', feedback);

  // 根据反馈决定下一步
  if (feedback && feedback.includes('approve')) {
    return new Command({
      goto: 'engineer',
    });
  } else {
    // 如果需要修改，回到PM节点
    return new Command({
      goto: 'pm',
      update: {
        humanFeedback: feedback?.feedback || '',
      },
    });
  }
}

// Engineer节点 - 生成任务
async function engineerNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('Engineer节点开始生成TASK JSON...');

  const llm = createLLM();

  const prompt = `
作为工程师，基于以下结构化PRD生成详细的TASK JSON和技术实现描述：

结构化PRD：
${state.structuredPRD}

${state.humanFeedback ? `人工反馈：${state.humanFeedback}` : ''}

请生成包含以下内容的JSON格式输出：
{
  "tasks": [
    {
      "id": "task_1",
      "title": "任务标题",
      "description": "任务描述",
      "priority": "high/medium/low",
      "estimated_hours": 8,
      "dependencies": [],
      "acceptance_criteria": ["标准1", "标准2"]
    }
  ],
  "technical_architecture": {
    "frontend": "技术栈描述",
    "backend": "技术栈描述",
    "database": "数据库设计"
  },
  "implementation_plan": {
    "phase1": "第一阶段计划",
    "phase2": "第二阶段计划"
  }
}
`;

  try {
    const response = await llm.invoke(prompt);
    const taskJSON =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    console.log('Engineer节点完成TASK JSON生成');

    return new Command({
      goto: 'engineerFeedback',
      update: {
        taskJSON: taskJSON,
      },
    });
  } catch (error) {
    console.error('Engineer节点处理失败:', error);
    throw error;
  }
}

// 人工反馈节点 - Engineer阶段
function engineerFeedbackNode(state: typeof StateAnnotation.State): Command {
  console.log('等待Engineer阶段人工反馈...');

  const feedback = interrupt({
    question: '请审核TASK JSON和技术实现方案',
    taskJSON: state.taskJSON,
    structuredPRD: state.structuredPRD,
    action: '请选择: approve(批准) 或 revise(修改)',
    instructions: '如果需要修改，请在feedback字段中提供具体的修改建议',
  });

  // 当恢复执行时，这里的逻辑会被执行
  console.log('收到Engineer反馈:', feedback);

  // 根据反馈决定下一步
  if (feedback && feedback.includes('approve')) {
    return new Command({
      goto: 'finalResponse',
    });
  } else {
    // 如果需要修改，回到Engineer节点
    return new Command({
      goto: 'engineer',
      update: {
        humanFeedback: feedback?.feedback || '',
      },
    });
  }
}

// 最终响应节点 - 生成完整的结构化响应
async function finalResponseNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('FinalResponse节点开始生成最终响应...');

  const llm = createLLM();

  const prompt = `
作为AI规划助手，请基于完成的PRD分析和任务分解，生成最终的完整响应：

结构化PRD：
${state.structuredPRD}

任务JSON：
${state.taskJSON}

请按照以下XML格式输出最终响应：

<evaluation>
  <is_prd_request>true</is_prd_request>
  <completeness>COMPLETE</completeness>
  <status>APPROVED</status>
</evaluation>
<content>
  <message>恭喜！您的PRD已经完成分析和任务分解。以下是完整的产品需求文档和开发任务列表。</message>
  <structured_prd>
    ${state.structuredPRD}
  </structured_prd>
  <task_json>
    ${state.taskJSON}
  </task_json>
  <summary>
    <total_tasks>从任务JSON中提取的任务总数</total_tasks>
    <estimated_duration>预估总开发时间</estimated_duration>
    <key_features>主要功能特性列表</key_features>
  </summary>
</content>

请确保：
1. 提供友好的完成消息
2. 包含完整的结构化PRD
3. 包含完整的任务JSON
4. 提供项目摘要信息
`;

  try {
    const response = await llm.invoke(prompt);
    const responseContent =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    console.log('FinalResponse节点完成最终响应生成');

    // 解析响应中的消息部分
    const messageMatch = responseContent.match(/<message>(.*?)<\/message>/s);
    const userMessage = messageMatch?.[1]?.trim() || '任务已完成！';

    return new Command({
      goto: END,
      update: {
        fullResponse: responseContent,
        isComplete: true,
        messages: [
          new AIMessage({
            content: userMessage,
            name: 'finalResponse',
          }),
        ],
      },
    });
  } catch (error) {
    console.error('FinalResponse节点处理失败:', error);
    throw error;
  }
}

// 创建并导出图
export function createGraph() {
  // 创建内存检查点
  const checkpointer = new MemorySaver();

  // 构建工作流图
  const graph = new StateGraph(StateAnnotation)
    .addNode('coordinator', coordinatorNode, {
      ends: ['tools', 'pm', END],
    })
    .addNode('tools', toolsNode, {
      ends: ['coordinator', 'pm'],
    })
    .addNode('pm', pmNode, {
      ends: ['engineer', 'pmFeedback'],
    })
    .addNode('pmFeedback', pmFeedbackNode, {
      ends: ['engineer', 'pm'],
    })
    .addNode('engineer', engineerNode, {
      ends: ['engineerFeedback'],
    })
    .addNode('engineerFeedback', engineerFeedbackNode, {
      ends: ['engineer', 'finalResponse'],
    })
    .addNode('finalResponse', finalResponseNode, {
      ends: [END],
    })
    .addEdge(START, 'coordinator')
    .compile({ checkpointer });

  return graph;
}

// 默认导出图实例
export default createGraph();
