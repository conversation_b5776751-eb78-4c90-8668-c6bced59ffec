#!/bin/bash

# PRD Planner Graph 调试启动脚本

echo "🚀 启动 PRD Planner Graph 调试环境"
echo "=================================="

# 检查是否安装了 LangGraph CLI
if ! command -v langgraph &> /dev/null; then
    echo "❌ LangGraph CLI 未安装"
    echo "请运行: pnpm add -g @langchain/langgraph-cli"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "❌ .env 文件不存在"
    echo "请确保 .env 文件包含必要的环境变量"
    exit 1
fi

echo "✅ 环境检查通过"

# 显示可用的命令
echo ""
echo "📋 可用命令:"
echo "1. 启动 LangGraph Studio (可视化调试)"
echo "   langgraph dev"
echo ""
echo "2. 运行测试脚本"
echo "   pnpm test"
echo ""
echo "3. 查看图结构"
echo "   langgraph graph --graph prd_planner"
echo ""
echo "4. 生成图可视化"
echo "   langgraph viz --graph prd_planner"
echo ""
echo "5. 运行图 (示例)"
echo '   langgraph run --graph prd_planner --input '"'"'{"originalPRD": "我需要开发一个用户管理系统"}'"'"''
echo ""

# 询问用户要执行的操作
echo "请选择要执行的操作:"
echo "1) 启动 LangGraph Studio"
echo "2) 运行测试脚本"
echo "3) 查看图结构"
echo "4) 生成图可视化"
echo "5) 退出"

read -p "请输入选项 (1-5): " choice

case $choice in
    1)
        echo "🎯 启动 LangGraph Studio..."
        langgraph dev
        ;;
    2)
        echo "🧪 运行测试脚本..."
        if command -v tsx &> /dev/null; then
            tsx test-graph.ts
        else
            echo "❌ tsx 未安装，请运行: pnpm add -g tsx"
        fi
        ;;
    3)
        echo "📊 查看图结构..."
        langgraph graph --graph prd_planner
        ;;
    4)
        echo "🎨 生成图可视化..."
        langgraph viz --graph prd_planner
        ;;
    5)
        echo "👋 退出"
        exit 0
        ;;
    *)
        echo "❌ 无效选项"
        exit 1
        ;;
esac
