# LangGraph PRD Planner 调试指南

这个目录包含了可以用 LangGraph CLI 调试的独立图实现。

## 文件说明

- `graph.ts` - 主要的图实现文件，包含所有节点和工作流逻辑
- `graph.service.ts` - NestJS 服务版本，用于集成到应用中
- `langgraph.json` - LangGraph CLI 配置文件
- `.env` - 环境变量配置
- `test-graph.ts` - 测试脚本
- `README.md` - 本说明文件

## 安装依赖

确保已安装 LangGraph CLI：

```bash
npm install -g @langchain/langgraph-cli
# 或者
pnpm add -g @langchain/langgraph-cli
```

## 使用 LangGraph CLI 调试

### 1. 启动 LangGraph Studio

在当前目录下运行：

```bash
cd apps/server-ai/src/planner/graph
langgraph dev
```

这将启动 LangGraph Studio，您可以在浏览器中可视化和调试图。

### 2. 使用 CLI 运行图

```bash
# 运行图
langgraph run --graph prd_planner --input '{"originalPRD": "我需要开发一个用户管理系统"}'

# 查看图结构
langgraph graph --graph prd_planner

# 查看图的可视化
langgraph viz --graph prd_planner
```

### 3. 运行测试脚本

```bash
# 使用 Node.js 运行测试
node --loader ts-node/esm test-graph.ts

# 或者使用 tsx
npx tsx test-graph.ts
```

## 图结构说明

### 节点说明

1. **coordinator** - 协调节点，判断用户输入是否为PRD请求或包含文档链接
2. **tools** - 工具节点，执行文档获取等工具操作
3. **pm** - 产品经理节点，将原始PRD转换为结构化PRD
4. **pmFeedback** - PM反馈节点，处理PRD完善的人工反馈
5. **engineer** - 工程师节点，基于PRD生成任务JSON
6. **engineerFeedback** - 工程师反馈节点，处理任务的人工反馈
7. **finalResponse** - 最终响应节点，生成完整的结构化响应

### 工作流程

```
START → coordinator → [tools] → pm → [pmFeedback] → engineer → [engineerFeedback] → finalResponse → END
```

### 状态字段

- `originalPRD` - 原始PRD输入
- `structuredPRD` - 结构化的PRD
- `taskJSON` - 生成的任务JSON
- `humanFeedback` - 人工反馈
- `isComplete` - 是否完成
- `fullResponse` - 完整响应
- `isPRDRequest` - 是否为PRD请求
- `coordinatorResponse` - 协调器响应
- `documentContent` - 文档内容
- `messages` - 消息历史

## 调试技巧

### 1. 查看日志

图中包含了详细的 console.log 输出，可以帮助您跟踪执行流程。

### 2. 使用断点

在 LangGraph Studio 中，您可以在任何节点设置断点来检查状态。

### 3. 修改输入

您可以在 Studio 中直接修改输入状态来测试不同的场景。

### 4. 查看中间状态

每个节点的输出状态都会被保存，您可以查看任何中间步骤的状态。

## 环境变量

确保在 `.env` 文件中配置了正确的环境变量：

- `OPENAI_API_KEY` - OpenAI API密钥
- `AI_GATEWAY_HOST` - AI网关主机地址
- `MODEL_NAME` - 使用的模型名称

## 与 NestJS 服务的区别

`graph.ts` 是独立版本，不依赖 NestJS 的依赖注入：

- 直接创建 LLM 实例而不是通过服务注入
- 使用模拟的文档获取函数而不是 OpenApiService
- 所有配置通过环境变量获取

这样设计是为了让图可以独立运行和调试，同时保持与服务版本的逻辑一致性。

## 故障排除

### 1. 模块导入错误

确保使用正确的导入语法和文件扩展名。

### 2. 环境变量未加载

检查 `.env` 文件是否在正确的位置，并且包含所需的变量。

### 3. API 调用失败

检查网络连接和 API 密钥是否正确。

### 4. 图编译错误

检查节点定义和边连接是否正确。
