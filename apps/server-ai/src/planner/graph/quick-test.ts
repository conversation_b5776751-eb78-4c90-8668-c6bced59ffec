import { createGraph } from './graph.js';
import { HumanMessage } from '@langchain/core/messages';

async function quickTest() {
  console.log('🚀 开始快速测试 PRD Planner Graph');
  console.log('=====================================');

  try {
    // 创建图实例
    const graph = createGraph();
    console.log('✅ 图创建成功');

    // 准备测试输入
    const testInput = {
      originalPRD: "我需要开发一个用户管理系统，包含用户注册、登录、个人信息管理等功能。",
      structuredPRD: "",
      taskJSON: "",
      humanFeedback: "",
      isComplete: false,
      fullResponse: "",
      isPRDRequest: false,
      coordinatorResponse: "",
      documentContent: "",
      messages: [new HumanMessage("我需要开发一个用户管理系统")]
    };

    console.log('📝 测试输入:', testInput.originalPRD);

    // 配置
    const config = { 
      configurable: { 
        thread_id: "test-" + Date.now() 
      } 
    };

    console.log('⏳ 开始执行图...');

    // 执行图
    const result = await graph.invoke(testInput, config);

    console.log('✅ 图执行完成!');
    console.log('📊 结果:');
    console.log('- isPRDRequest:', result.isPRDRequest);
    console.log('- isComplete:', result.isComplete);
    console.log('- 消息数量:', result.messages?.length || 0);
    
    if (result.structuredPRD) {
      console.log('- 结构化PRD长度:', result.structuredPRD.length);
    }
    
    if (result.taskJSON) {
      console.log('- 任务JSON长度:', result.taskJSON.length);
    }

    if (result.coordinatorResponse) {
      console.log('- 协调器响应:', result.coordinatorResponse.substring(0, 100) + '...');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    // 提供一些调试建议
    if (error.message.includes('Chat is not supported')) {
      console.log('💡 建议: 检查状态定义中的 messages 字段');
    }
    
    if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      console.log('💡 建议: 检查网络连接和API配置');
    }
    
    if (error.message.includes('401') || error.message.includes('403')) {
      console.log('💡 建议: 检查API密钥配置');
    }
  }
}

// 运行测试
quickTest().catch(console.error);
