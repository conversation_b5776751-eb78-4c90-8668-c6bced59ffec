import { createGraph } from './graph.js';
import { HumanMessage } from '@langchain/core/messages';

async function streamDebug() {
  console.log('🔄 开始流式调试 PRD Planner Graph');
  console.log('===================================');

  try {
    const graph = createGraph();
    console.log('✅ 图创建成功');

    const testInput = {
      originalPRD: "我需要开发一个在线购物系统，包含商品展示、购物车、订单管理等功能。",
      structuredPRD: "",
      taskJSON: "",
      humanFeedback: "",
      isComplete: false,
      fullResponse: "",
      isPRDRequest: false,
      coordinatorResponse: "",
      documentContent: "",
      messages: [new HumanMessage("我需要开发一个在线购物系统")]
    };

    const config = { 
      configurable: { 
        thread_id: "stream-debug-" + Date.now() 
      } 
    };

    console.log('📝 输入:', testInput.originalPRD);
    console.log('⏳ 开始流式执行...\n');

    // 使用流式模式观察每个节点的执行
    for await (const chunk of await graph.stream(testInput, {
      ...config,
      streamMode: "updates"
    })) {
      console.log('📦 收到更新:', Object.keys(chunk));
      
      for (const [nodeName, nodeOutput] of Object.entries(chunk)) {
        console.log(`🔸 节点: ${nodeName}`);
        
        if (nodeOutput && typeof nodeOutput === 'object') {
          // 显示关键字段的变化
          const keys = Object.keys(nodeOutput);
          console.log(`  📋 更新字段: ${keys.join(', ')}`);
          
          // 显示一些关键信息
          if ('isPRDRequest' in nodeOutput) {
            console.log(`  🎯 PRD请求: ${nodeOutput.isPRDRequest}`);
          }
          
          if ('isComplete' in nodeOutput) {
            console.log(`  ✅ 完成状态: ${nodeOutput.isComplete}`);
          }
          
          if ('messages' in nodeOutput && Array.isArray(nodeOutput.messages)) {
            console.log(`  💬 消息数量: ${nodeOutput.messages.length}`);
            // 显示最后一条消息的简要信息
            if (nodeOutput.messages.length > 0) {
              const lastMsg = nodeOutput.messages[nodeOutput.messages.length - 1];
              console.log(`  📝 最新消息: ${lastMsg.content?.substring(0, 50)}...`);
            }
          }
          
          if ('structuredPRD' in nodeOutput && nodeOutput.structuredPRD) {
            console.log(`  📋 结构化PRD: ${nodeOutput.structuredPRD.length} 字符`);
          }
          
          if ('taskJSON' in nodeOutput && nodeOutput.taskJSON) {
            console.log(`  📊 任务JSON: ${nodeOutput.taskJSON.length} 字符`);
          }
        }
        
        console.log(''); // 空行分隔
      }
      
      console.log('─'.repeat(50));
    }

    console.log('🏁 流式执行完成!');

  } catch (error) {
    console.error('❌ 流式调试失败:', error);
    console.log('\n🔍 错误详情:');
    console.log('消息:', error.message);
    if (error.stack) {
      console.log('堆栈:', error.stack.split('\n').slice(0, 5).join('\n'));
    }
  }
}

// 运行流式调试
streamDebug().catch(console.error);
